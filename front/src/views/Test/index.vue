<template>
  <div class="profile-container">
    <!-- 简洁的头部区域 -->
    <div class="profile-header">
      <div class="header-content">
        <div class="avatar-section">
          <div class="avatar-wrapper">
            <t-avatar size="100px" :image="userInfo.avatar" class="user-avatar" />
            <div class="avatar-upload" @click="handleAvatarUpload">
              <camera-icon :size="16" />
            </div>
          </div>
        </div>
        <div class="user-info">
          <h1 class="user-name">{{ userInfo.name }}</h1>
          <p class="user-email">{{ userInfo.email }}</p>
          <div class="user-status">
            <t-tag theme="success" variant="light">
              <shield-check-icon :size="14" />
              已验证
            </t-tag>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-container">
        <!-- 基本信息编辑 -->
        <div class="info-section">
          <div class="section-header">
            <h2 class="section-title">基本信息</h2>
            <p class="section-desc">管理您的个人资料信息</p>
          </div>

          <div class="form-container">
            <t-form :data="userInfo" label-width="80px" class="profile-form">
              <div class="form-row">
                <t-form-item label="用户名" class="form-item">
                  <t-input
                    v-model="userInfo.name"
                    placeholder="请输入用户名"
                    class="form-input"
                  />
                </t-form-item>

                <t-form-item label="邮箱" class="form-item">
                  <t-input
                    v-model="userInfo.email"
                    placeholder="请输入邮箱地址"
                    class="form-input"
                  />
                </t-form-item>
              </div>

              <div class="form-row">
                <t-form-item label="手机号" class="form-item">
                  <t-input
                    v-model="userInfo.phone"
                    placeholder="请输入手机号码"
                    class="form-input"
                  />
                </t-form-item>

                <t-form-item label="职位" class="form-item">
                  <t-input
                    v-model="userInfo.title"
                    placeholder="请输入职位"
                    class="form-input"
                  />
                </t-form-item>
              </div>

              <t-form-item label="个人简介" class="form-item-full">
                <t-textarea
                  v-model="userInfo.bio"
                  placeholder="介绍一下自己..."
                  :maxlength="200"
                  class="form-textarea"
                  :autosize="{ minRows: 4, maxRows: 6 }"
                />
              </t-form-item>

              <div class="form-actions">
                <t-button theme="primary" size="large" @click="handleSave">
                  <save-icon :size="18" />
                  保存更改
                </t-button>
                <t-button variant="outline" size="large" @click="handleReset">
                  重置
                </t-button>
              </div>
            </t-form>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>



<script setup>
import { reactive } from 'vue'
import {
  CameraIcon,
  ShieldCheckIcon,
  SaveIcon
} from 'lucide-vue-next'
import { MessagePlugin } from 'tdesign-vue-next'

// Mock 用户信息
const userInfo = reactive({
  name: '张小明',
  email: '<EMAIL>',
  phone: '13800138000',
  title: '高级前端工程师',
  bio: '热爱技术，专注于前端开发和用户体验设计。拥有5年以上的开发经验，擅长Vue.js、React等现代前端框架。',
  avatar: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face'
})

// 保存原始数据用于重置
const originalUserInfo = { ...userInfo }

// 处理头像上传
const handleAvatarUpload = () => {
  MessagePlugin.info('头像上传功能开发中...')
}

// 保存更改
const handleSave = () => {
  MessagePlugin.success('个人信息保存成功！')
}

// 重置表单
const handleReset = () => {
  Object.assign(userInfo, originalUserInfo)
  MessagePlugin.info('表单已重置')
}
</script>

<style scoped>
/* 全局容器 */
.profile-container {
  min-height: 100vh;
  background: #f8fafc;
}

/* 头部区域 */
.profile-header {
  background: linear-gradient(135deg, #3b82f6 0%, #1e40af 100%);
  padding: 40px 0;
  color: white;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
  display: flex;
  align-items: center;
  gap: 32px;
}

/* 头像区域 */
.avatar-section {
  flex-shrink: 0;
}

.avatar-wrapper {
  position: relative;
  display: inline-block;
}

.user-avatar {
  border: 4px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
}

.avatar-upload {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 32px;
  height: 32px;
  background: #3b82f6;
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: white;
}

.avatar-upload:hover {
  background: #2563eb;
  transform: scale(1.1);
}

/* 用户信息 */
.user-info {
  flex: 1;
}

.user-name {
  font-size: 2rem;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: white;
}

.user-email {
  font-size: 1rem;
  opacity: 0.9;
  margin: 0 0 16px 0;
}

.user-status {
  display: flex;
  gap: 8px;
}

/* 主要内容区域 */
.main-content {
  background: #f8fafc;
  min-height: calc(100vh - 200px);
  padding: 40px 0;
}

.content-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 信息编辑区域 */
.info-section {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  padding: 40px;
}

.section-header {
  margin-bottom: 32px;
  text-align: center;
}

.section-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.section-desc {
  font-size: 1rem;
  color: #64748b;
  margin: 0;
}

/* 表单样式 */
.form-container {
  max-width: 100%;
}

.profile-form {
  max-width: 100%;
}

.form-row {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 24px;
  margin-bottom: 24px;
}

.form-item {
  display: flex;
  flex-direction: column;
}

.form-item-full {
  margin-bottom: 24px;
}

.form-input,
.form-textarea {
  border-radius: 8px;
  border: 2px solid #e5e7eb;
  transition: all 0.2s ease;
  padding: 12px 16px;
}

.form-input:focus,
.form-textarea:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

.form-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-top: 32px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
    gap: 24px;
  }

  .content-container {
    padding: 0 16px;
  }

  .info-section {
    padding: 24px;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .form-actions {
    flex-direction: column;
    align-items: center;
  }

  .user-name {
    font-size: 1.5rem;
  }
}
</style>


</style>
