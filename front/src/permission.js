/**
 * 路由权限控制
 * 参考tellyou/permission.ts设计，集成动态路由功能
 * 保留全屏加载动画功能
 */
import router from '@/router'
import { useUserStore, usePermissionStore } from '@/store'
import { MessagePlugin } from 'tdesign-vue-next'
import { createApp } from 'vue'
import PageLoading from '@/components/PageLoading.vue'
import { isPathMatch } from '@/utils/validate'

// 全屏加载状态
let loadingInstance = null
let loadingStartTime = null
let errorRetry = 0

// 路由白名单检查
const isWhiteList = (path, whiteList) => {
  return whiteList.some((pattern) => isPathMatch(pattern, path))
}

/**
 * 前置路由守卫 - 参考tellyou/permission.ts设计，保留全屏加载动画
 */
router.beforeEach(async (to, from, next) => {
  // 开始全屏加载动画
  if (loadingInstance) {
    document.body.removeChild(loadingInstance)
    loadingInstance = null
  }

  loadingStartTime = Date.now()

  // 创建自定义加载组件
  const loadingDiv = document.createElement('div')
  loadingDiv.id = 'page-loading-container'
  document.body.appendChild(loadingDiv)

  const loadingApp = createApp(PageLoading, {
    visible: true,
    text: '页面加载中'
  })

  loadingInstance = loadingDiv
  loadingApp.mount(loadingDiv)

  const permissionStore = usePermissionStore()
  const { whiteListRouters } = permissionStore

  const userStore = useUserStore()
  const { token } = userStore



  if (token) {
    // 错误重试超出限制
    if (errorRetry >= 3) {
      errorRetry = 0
      next({
        path: '/500',
        query: { redirect: encodeURIComponent(to.fullPath) },
      })
      return
    }

    if (isWhiteList(to.path, whiteListRouters)) {
      next()
      return
    }

    const { roles } = userStore

    if (roles && roles.length > 0) {
      // 用户已有角色信息，检查是否已生成动态路由
      if (permissionStore.menus.length === 0) {
        try {
          await permissionStore.generateRoutes()
          next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
          errorRetry = 0
        } catch (error) {
          console.error('生成动态路由失败:', error)
          errorRetry++
          next({
            path: '/login',
            query: { redirect: encodeURIComponent(to.fullPath) },
          })
        }
      } else {
        next()
      }
    } else {
      try {
        await userStore.getUserInfo()
        await permissionStore.generateRoutes()
        next({ ...to, replace: true }) // hack方法 确保addRoutes已完成
        errorRetry = 0
      } catch (error) {
        console.error('获取用户信息或路由失败:', error)
        errorRetry++
        next({
          path: '/login',
          query: { redirect: encodeURIComponent(to.fullPath) },
        })
      }
    }
  } else {
    /* white list router */
    if (whiteListRouters.indexOf(to.path) !== -1) {
      next()
    } else {
      next({
        path: '/login',
        query: { redirect: encodeURIComponent(to.fullPath) },
      })
    }
  }
})

/**
 * 后置路由守卫 - 参考tellyou/permission.ts设计，保留全屏加载动画
 */
router.afterEach(async (to, from) => {
  // 结束全屏加载动画
  if (loadingInstance) {
    const loadingDuration = Date.now() - loadingStartTime
    const minLoadingTime = 500 // 最小显示时间500ms
    const additionalDelay = 300 // 额外延迟300ms确保页面渲染完成

    // 计算需要延迟的时间
    const remainingTime = Math.max(0, minLoadingTime - loadingDuration)
    const totalDelay = remainingTime + additionalDelay

    setTimeout(() => {
      if (loadingInstance) {
        document.body.removeChild(loadingInstance)
        loadingInstance = null
        loadingStartTime = null
      }
    }, totalDelay)
  }

  if (to.path === '/login') {
    const userStore = useUserStore()
    const { token } = userStore
    const isLogin = await userStore.isLogin()
    if (isLogin) {
      const redirect = to.query.redirect
      const redirectUrl = redirect ? decodeURIComponent(redirect) : '/'
      await router.push(redirectUrl)
    } else if (token) {
      await userStore.logout()
    }
  }

  // 设置页面标题
  if (to.meta && to.meta.title) {
    document.title = `${to.meta.title} - MailCode`
  } else {
    document.title = 'MailCode - 无限邮箱系统'
  }

  // 记录页面访问日志（开发环境）
  if (import.meta.env.DEV) {
    console.log(`🚀 路由跳转: ${from.path} → ${to.path}`)
  }

  // 页面滚动到顶部
  window.scrollTo(0, 0)
})

/**
 * 路由错误处理
 */
router.onError((error) => {
  // 立即关闭全屏加载动画
  if (loadingInstance) {
    document.body.removeChild(loadingInstance)
    loadingInstance = null
    loadingStartTime = null
  }

  console.error('路由错误:', error)
  MessagePlugin.error('页面加载失败，请刷新重试')
})


